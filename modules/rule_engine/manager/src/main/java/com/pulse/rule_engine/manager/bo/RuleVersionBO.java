package com.pulse.rule_engine.manager.bo;

import com.pulse.rule_engine.persist.dos.RuleVersion;
import com.pulse.rule_engine.manager.adapter.RuleEngineAdapterFactory;
import com.pulse.rule_engine.manager.adapter.RuleEngineAdapter;
import com.pulse.rule_engine.manager.RuleVersionBaseDtoManager;
import com.pulse.rule_engine.manager.dto.RuleVersionBaseDto;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;
import cn.hutool.extra.spring.SpringUtil;
import com.pulse.pulse.common.utils.SemanticVersionUtils;
import com.pulse.pulse.common.enums.VersionChangeType;
import com.pulse.rule_engine.common.enums.RuleStatusEnum;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import org.springframework.util.StringUtils;
import org.springframework.util.CollectionUtils;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

@DynamicInsert
@Where(clause = "deleted_at = 0 ")
@SQLDelete(
        sql =
                "UPDATE rule_version  SET deleted_at = (EXTRACT(DAY FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 24 * 60 *"
                    + " 60 * 1000 + EXTRACT(HOUR FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01"
                    + " 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 60 * 60 * 1000 + EXTRACT(MINUTE"
                    + " FROM (CURRENT_TIMESTAMP - TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD"
                    + " HH24:MI:SS'))) * 60 * 1000 + EXTRACT(SECOND FROM (CURRENT_TIMESTAMP -"
                    + " TO_TIMESTAMP('1970-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS'))) * 1000)"
                    + " WHERE id = ?")
@Getter
@Setter
@Slf4j
@Table(name = "rule_version")
@Entity
@AutoGenerated(locked = true, uuid = "7f3e312f-a4ff-484e-bc1d-dd4cb00ec2a6|BO|DEFINITION")
public class RuleVersionBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 审批人ID 审批人ID，外键关联用户表 */
    @Column(name = "approver_id")
    @AutoGenerated(locked = true, uuid = "2116874c-2ce1-452c-bba3-cc44929b54fc")
    private String approverId;

    /** 创建时间 */
    @Column(name = "created_at", updatable = false)
    @AutoGenerated(locked = true, uuid = "c66c0a24-09f7-5479-91c9-e318f85efa87")
    private Date createdAt;

    /** 创建人 创建人ID，关联用户表 */
    @Column(name = "created_by")
    @AutoGenerated(locked = true, uuid = "65a19061-fc93-4461-a4b6-3b331b0c9db9")
    private String createdBy;

    /** 删除时间 */
    @Column(name = "deleted_at")
    @AutoGenerated(locked = true, uuid = "728c1346-4675-551b-8a9e-c599097e5d6a")
    private Long deletedAt = 0L;

    /** 决策规则内容 DRL 规则内容，如 "rule '抗生素特殊使用'..." */
    @Column(name = "drl_content")
    @AutoGenerated(locked = true, uuid = "311af77a-ed86-4504-9e3b-9833677977b1")
    private String drlContent;

    /** 有效结束时间 生效结束时间，可为空表示一直生效 */
    @Column(name = "effective_end_time")
    @AutoGenerated(locked = true, uuid = "ac909e52-f578-4233-8ec1-4f81b472e12f")
    private Date effectiveEndTime;

    /** 生效开始时间 生效开始时间 */
    @Column(name = "effective_start_time")
    @AutoGenerated(locked = true, uuid = "604d4977-d9f2-4926-b184-45fead26dd86")
    private Date effectiveStartTime;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "c7a2e6e9-3d77-4bfb-896d-30a2df4909e3")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    @ManyToOne
    @JoinColumn(name = "rule_id", referencedColumnName = "id")
    @AutoGenerated(locked = true)
    private RuleBO ruleBO;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "c4d5cf4c-f8ad-57b9-b064-93acc38b8941")
    private Date updatedAt;

    /** 更新人 更新人ID，关联用户表 */
    @Column(name = "updated_by")
    @AutoGenerated(locked = true, uuid = "edc3e01d-8e42-4497-acf7-b69dcc98a7ae")
    private String updatedBy;

    /**
     * 版本号 版本号,如 "2.1.3"。 - HIS 系统中的规则版本需反映变化的性质（如新功能、政策调整），时间戳和自增ID缺乏可读性。 -
     * 语义化版本便于开发、运维和业务人员理解版本演进，支持版本回滚和兼容性判断。
     */
    @Column(name = "version_number")
    @AutoGenerated(locked = true, uuid = "800fe213-fdd4-4ea8-87fa-b1d62b32b27b")
    private String versionNumber;

    /** 校验当前BO的数据，在新增和变更的时候回调 */
    @AutoGenerated(locked = true, uuid = "964940a5-694a-4c15-87a2-b422cc65fafe|BO|VALIDATOR")
    @Override
    public void validate() {
        log.debug("开始校验规则版本数据，版本号: {}, 规则ID: {}", this.versionNumber, this.getRuleId());

        // 1. 基础字段校验
        validateBasicFields();

        // 2. 版本号格式校验
        validateVersionNumber();

        // 3. 时间有效性校验
        validateEffectiveTime();

        // 4. DRL内容语法校验
        validateDrlContent();

        // 5. 版本唯一性校验
        validateVersionUniqueness();

        // 6. 业务规则校验
        validateBusinessRules();

        log.debug("规则版本数据校验完成");
    }

    /**
     * 基础字段校验
     */
    private void validateBasicFields() {
        // 校验版本号不能为空
        if (!StringUtils.hasText(this.versionNumber)) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "版本号不能为空");
        }

        // 校验规则ID不能为空
        if (this.ruleBO == null || !StringUtils.hasText(this.ruleBO.getId())) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "规则ID不能为空");
        }

        // 校验生效开始时间不能为空
        if (this.effectiveStartTime == null) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "生效开始时间不能为空");
        }

        // 校验DRL内容不能为空
        if (!StringUtils.hasText(this.drlContent)) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER, "DRL规则内容不能为空");
        }
    }

    /**
     * 版本号格式校验
     */
    private void validateVersionNumber() {
        try {
            // 使用语义化版本工具类验证版本号格式
            if (!SemanticVersionUtils.isValidVersion(this.versionNumber)) {
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER,
                    "版本号格式不正确，应遵循语义化版本规范（如：1.0.0、2.1.3）");
            }

            // 检查是否为稳定版本（主版本号大于0）
            if (!SemanticVersionUtils.isStableVersion(this.versionNumber)) {
                log.warn("当前版本号为预发布版本（主版本号为0）: {}", this.versionNumber);
            }

        } catch (Exception e) {
            throw new IgnoredException(ErrorCode.WRONG_PARAMETER,
                "版本号验证失败: " + e.getMessage());
        }
    }

    /**
     * 时间有效性校验
     */
    private void validateEffectiveTime() {
        Date now = new Date();

        // 校验生效开始时间不能早于当前时间太久（避免历史数据错误）
        long daysBefore = (now.getTime() - this.effectiveStartTime.getTime()) / (1000 * 60 * 60 * 24);
        if (daysBefore > 365) { // 超过一年
            log.warn("生效开始时间距离当前时间超过一年: {}", this.effectiveStartTime);
        }

        // 校验生效结束时间必须晚于开始时间
        if (this.effectiveEndTime != null) {
            if (this.effectiveEndTime.before(this.effectiveStartTime)) {
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER,
                    "生效结束时间不能早于生效开始时间");
            }

            // 校验生效结束时间不能早于当前时间（除非是历史版本）
            if (this.effectiveEndTime.before(now)) {
                log.warn("生效结束时间早于当前时间，该版本可能已过期: {}", this.effectiveEndTime);
            }
        }
    }

    /**
     * DRL内容语法校验
     */
    private void validateDrlContent() {
        try {
            // 获取规则引擎适配器工厂
            RuleEngineAdapterFactory adapterFactory = SpringUtil.getBean(RuleEngineAdapterFactory.class);
            if (adapterFactory != null) {
                // 获取默认的规则引擎适配器
                RuleEngineAdapter adapter = adapterFactory.getDefaultAdapter();

                // 验证DRL语法
                Boolean isValid = adapter.validateRuleSyntax(this.drlContent);
                if (!isValid) {
                    throw new IgnoredException(ErrorCode.WRONG_PARAMETER,
                        "DRL规则内容语法错误，请检查规则语法");
                }

                log.debug("DRL语法验证通过");
            } else {
                log.warn("无法获取规则引擎适配器工厂，跳过DRL语法验证");
            }
        } catch (IgnoredException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            // 其他异常转换为业务异常
            throw new IgnoredException(ErrorCode.SYS_ERROR,
                "DRL规则语法验证失败: " + e.getMessage());
        }
    }

    /**
     * 版本唯一性校验
     */
    private void validateVersionUniqueness() {
        try {
            // 获取RuleVersionBaseDtoManager
            RuleVersionBaseDtoManager ruleVersionManager = SpringUtil.getBean(RuleVersionBaseDtoManager.class);
            if (ruleVersionManager != null) {
                // 构建查询条件
                RuleVersion.RuleIdAndVersionNumber query = new RuleVersion.RuleIdAndVersionNumber();
                query.setRuleId(this.getRuleId());
                query.setVersionNumber(this.versionNumber);

                // 查询是否已存在相同规则ID和版本号的记录
                RuleVersionBaseDto existingVersion = ruleVersionManager.getByRuleIdAndVersionNumber(query);

                // 如果存在且不是当前记录，则抛出异常
                if (existingVersion != null && !existingVersion.getId().equals(this.id)) {
                    throw new IgnoredException(ErrorCode.WRONG_PARAMETER,
                        String.format("规则ID [%s] 的版本号 [%s] 已存在，请使用其他版本号",
                            this.getRuleId(), this.versionNumber));
                }

                log.debug("版本唯一性校验通过");
            } else {
                log.warn("无法获取RuleVersionBaseDtoManager，跳过版本唯一性校验");
            }
        } catch (IgnoredException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("版本唯一性校验失败", e);
            throw new IgnoredException(ErrorCode.SYS_ERROR,
                "版本唯一性校验失败: " + e.getMessage());
        }
    }

    /**
     * 业务规则校验
     */
    private void validateBusinessRules() {
        // 校验规则状态与版本的关系
        validateRuleStatusAndVersion();

        // 校验版本递增规则
        validateVersionIncrement();

        // 校验时间重叠
        validateTimeOverlap();
    }

    /**
     * 校验规则状态与版本的关系
     */
    private void validateRuleStatusAndVersion() {
        if (this.ruleBO != null && this.ruleBO.getStatus() != null) {
            RuleStatusEnum ruleStatus = this.ruleBO.getStatus();

            // 如果规则状态为已下线，不允许创建新版本
            if (RuleStatusEnum.OFFLINE.equals(ruleStatus)) {
                throw new IgnoredException(ErrorCode.WRONG_PARAMETER,
                    "已下线的规则不允许创建新版本");
            }

            // 如果规则状态为生效，检查当前版本是否与规则的当前版本一致
            if (RuleStatusEnum.EFFECTIVE.equals(ruleStatus)) {
                String currentVersion = this.ruleBO.getCurrentVersion();
                if (StringUtils.hasText(currentVersion) && !currentVersion.equals(this.versionNumber)) {
                    log.warn("规则状态为生效，但当前版本号与规则的当前版本不一致。规则当前版本: {}, 当前版本号: {}",
                        currentVersion, this.versionNumber);
                }
            }
        }
    }

    /**
     * 校验版本递增规则
     */
    private void validateVersionIncrement() {
        try {
            // 获取同一规则的所有版本
            RuleVersionBaseDtoManager ruleVersionManager = SpringUtil.getBean(RuleVersionBaseDtoManager.class);
            if (ruleVersionManager != null) {
                List<RuleVersionBaseDto> existingVersions = ruleVersionManager.getByRuleId(this.getRuleId());

                if (!CollectionUtils.isEmpty(existingVersions)) {
                    // 获取最新版本号
                    String latestVersion = getLatestVersion(existingVersions);

                    if (StringUtils.hasText(latestVersion)) {
                        // 比较当前版本号与最新版本号
                        int comparison = SemanticVersionUtils.compareVersions(this.versionNumber, latestVersion);

                        // 如果当前版本号不大于最新版本号，给出警告
                        if (comparison <= 0) {
                            log.warn("当前版本号 [{}] 不大于已存在的最新版本号 [{}]，请确认版本递增是否正确",
                                this.versionNumber, latestVersion);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("版本递增规则校验失败: {}", e.getMessage());
            // 版本递增校验失败不阻止保存，只记录警告
        }
    }

    /**
     * 校验时间重叠
     */
    private void validateTimeOverlap() {
        try {
            // 获取同一规则的所有版本
            RuleVersionBaseDtoManager ruleVersionManager = SpringUtil.getBean(RuleVersionBaseDtoManager.class);
            if (ruleVersionManager != null) {
                List<RuleVersionBaseDto> existingVersions = ruleVersionManager.getByRuleId(this.getRuleId());

                if (!CollectionUtils.isEmpty(existingVersions)) {
                    for (RuleVersionBaseDto existingVersion : existingVersions) {
                        // 跳过当前记录
                        if (existingVersion.getId().equals(this.id)) {
                            continue;
                        }

                        // 检查时间重叠
                        if (isTimeOverlap(existingVersion)) {
                            log.warn("检测到时间重叠：当前版本 [{}] 的生效时间与版本 [{}] 存在重叠",
                                this.versionNumber, existingVersion.getVersionNumber());
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("时间重叠校验失败: {}", e.getMessage());
            // 时间重叠校验失败不阻止保存，只记录警告
        }
    }

    /**
     * 获取最新版本号
     */
    private String getLatestVersion(List<RuleVersionBaseDto> versions) {
        if (CollectionUtils.isEmpty(versions)) {
            return null;
        }

        return versions.stream()
            .map(RuleVersionBaseDto::getVersionNumber)
            .filter(StringUtils::hasText)
            .filter(SemanticVersionUtils::isValidVersion)
            .max((v1, v2) -> SemanticVersionUtils.compareVersions(v1, v2))
            .orElse(null);
    }

    /**
     * 检查时间是否重叠
     */
    private boolean isTimeOverlap(RuleVersionBaseDto otherVersion) {
        Date thisStart = this.effectiveStartTime;
        Date thisEnd = this.effectiveEndTime;
        Date otherStart = otherVersion.getEffectiveStartTime();
        Date otherEnd = otherVersion.getEffectiveEndTime();

        // 如果任一版本的开始时间为空，无法判断重叠
        if (thisStart == null || otherStart == null) {
            return false;
        }

        // 如果当前版本没有结束时间，视为永久有效
        if (thisEnd == null) {
            thisEnd = new Date(Long.MAX_VALUE);
        }

        // 如果对比版本没有结束时间，视为永久有效
        if (otherEnd == null) {
            otherEnd = new Date(Long.MAX_VALUE);
        }

        // 检查时间区间是否重叠
        return thisStart.before(otherEnd) && thisEnd.after(otherStart);
    }

    @AutoGenerated(locked = true)
    public RuleVersion convertToRuleVersion() {
        RuleVersion entity = new RuleVersion();
        BoUtil.copyProperties(
                this,
                entity,
                "id",
                "versionNumber",
                "drlContent",
                "effectiveStartTime",
                "effectiveEndTime",
                "approverId",
                "createdBy",
                "updatedBy",
                "createdAt",
                "updatedAt",
                "deletedAt");
        RuleBO ruleBO = this.getRuleBO();
        entity.setRuleId(ruleBO.getId());
        return entity;
    }

    @AutoGenerated(locked = true)
    public String getApproverId() {
        return this.approverId;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getCreatedBy() {
        return this.createdBy;
    }

    @AutoGenerated(locked = true)
    public Long getDeletedAt() {
        return this.deletedAt;
    }

    @AutoGenerated(locked = true)
    public String getDrlContent() {
        return this.drlContent;
    }

    @AutoGenerated(locked = true)
    public Date getEffectiveEndTime() {
        return this.effectiveEndTime;
    }

    @AutoGenerated(locked = true)
    public Date getEffectiveStartTime() {
        return this.effectiveStartTime;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public RuleBO getRuleBO() {
        return this.ruleBO;
    }

    @AutoGenerated(locked = true)
    public String getRuleId() {
        return this.getRuleBO().getId();
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public String getUpdatedBy() {
        return this.updatedBy;
    }

    @AutoGenerated(locked = true)
    public String getVersionNumber() {
        return this.versionNumber;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setApproverId(String approverId) {
        this.approverId = approverId;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setDeletedAt(Long deletedAt) {
        this.deletedAt = deletedAt;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setDrlContent(String drlContent) {
        this.drlContent = drlContent;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setEffectiveEndTime(Date effectiveEndTime) {
        this.effectiveEndTime = effectiveEndTime;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setEffectiveStartTime(Date effectiveStartTime) {
        this.effectiveStartTime = effectiveStartTime;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setId(String id) {
        this.id = id;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setRuleBO(RuleBO ruleBO) {
        this.ruleBO = ruleBO;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
        return (RuleVersionBO) this;
    }

    @AutoGenerated(locked = true)
    public RuleVersionBO setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber;
        return (RuleVersionBO) this;
    }
}
