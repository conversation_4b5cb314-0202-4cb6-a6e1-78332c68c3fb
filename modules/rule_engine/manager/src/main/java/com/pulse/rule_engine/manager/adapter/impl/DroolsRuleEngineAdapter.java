package com.pulse.rule_engine.manager.adapter.impl;

import com.pulse.rule_engine.manager.dto.RuleVersionBaseDto;
import com.pulse.rule_engine.manager.adapter.RuleEngineAdapter;
import com.pulse.rule_engine.manager.config.RuleEngineConfig;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.drools.compiler.kie.builder.impl.InternalKieModule;
import org.drools.compiler.kie.builder.impl.KieBuilderImpl;
import org.kie.api.KieServices;
import org.kie.api.builder.KieBuilder;
import org.kie.api.builder.KieFileSystem;
import org.kie.api.builder.Message;
import org.kie.api.builder.Results;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/** Drools规则引擎适配器实现 基于Drools框架实现规则引擎的具体功能 */
@Slf4j
@Component
public class DroolsRuleEngineAdapter implements RuleEngineAdapter {

    @Autowired private RuleEngineConfig ruleEngineConfig;

    /** 规则容器缓存，key为规则内容的hash值 */
    private final Map<String, KieContainer> containerCache = new ConcurrentHashMap<>();

    @Override
    public Boolean checkRuleConflict(
            List<RuleVersionBaseDto> ruleVersionList, String businessData) {
        if (CollectionUtils.isEmpty(ruleVersionList) || !StringUtils.hasText(businessData)) {
            return false;
        }

        try {
            // 构建包含所有规则的KieContainer
            KieContainer kieContainer = buildKieContainer(ruleVersionList);

            // 创建KieSession进行冲突检测
            KieSession kieSession = kieContainer.newKieSession();

            try {
                // 解析并插入业务数据进行冲突检测
                Object businessObject = parseBusinessData(businessData);
                kieSession.insert(businessObject);

                // 添加冲突检测标记
                Map<String, Object> conflictContext = new HashMap<>();
                conflictContext.put("conflictDetection", true);
                conflictContext.put("firedRules", new ArrayList<String>());
                kieSession.insert(conflictContext);

                // 执行规则并检测冲突
                int firedRules = kieSession.fireAllRules();

                // 检测是否存在冲突
                boolean hasConflict =
                        detectConflictFromFiredRules(kieSession, firedRules, conflictContext);

                log.info(
                        "规则冲突检测完成，规则数量: {}, 触发规则数: {}, 是否存在冲突: {}",
                        ruleVersionList.size(),
                        firedRules,
                        hasConflict);

                return hasConflict;

            } finally {
                kieSession.dispose();
            }

        } catch (Exception e) {
            log.error("规则冲突检测失败", e);
            throw new IgnoredException(ErrorCode.SYS_ERROR, "规则冲突检测失败: " + e.getMessage());
        }
    }

    @Override
    public Boolean executeRule(List<RuleVersionBaseDto> ruleVersionList, String businessData) {
        if (CollectionUtils.isEmpty(ruleVersionList) || !StringUtils.hasText(businessData)) {
            return false;
        }

        try {
            // 构建KieContainer
            KieContainer kieContainer = buildKieContainer(ruleVersionList);

            // 创建KieSession
            KieSession kieSession = kieContainer.newKieSession();

            try {
                // 解析业务数据并插入到工作内存
                Object businessObject = parseBusinessData(businessData);
                kieSession.insert(businessObject);

                // 执行规则
                int firedRules = kieSession.fireAllRules();

                log.info("规则执行完成，规则数量: {}, 触发规则数: {}", ruleVersionList.size(), firedRules);

                return firedRules > 0;

            } finally {
                kieSession.dispose();
            }

        } catch (Exception e) {
            log.error("规则执行失败", e);
            throw new IgnoredException(ErrorCode.SYS_ERROR, "规则执行失败: " + e.getMessage());
        }
    }

    @Override
    public Boolean validateRuleSyntax(String drlContent) {
        if (!StringUtils.hasText(drlContent)) {
            return false;
        }

        try {
            KieServices kieServices = KieServices.Factory.get();
            KieFileSystem kieFileSystem = kieServices.newKieFileSystem();

            // 使用虚拟路径在内存中添加规则内容，不写入实际文件
            String virtualPath =
                    "src/main/resources/rules/validation_" + System.currentTimeMillis() + ".drl";
            kieFileSystem.write(virtualPath, drlContent);

            log.debug("规则语法验证：DRL内容已加载到内存虚拟路径: {}", virtualPath);

            // 构建
            KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
            kieBuilder.buildAll();

            // 检查构建结果
            Results results = kieBuilder.getResults();
            if (results.hasMessages(Message.Level.ERROR)) {
                log.warn("规则语法验证失败: {}", results.getMessages());
                return false;
            }

            log.debug("规则语法验证通过");
            return true;

        } catch (Exception e) {
            log.error("规则语法验证异常", e);
            return false;
        }
    }

    @Override
    public String getAdapterType() {
        return "drools";
    }

    /** 构建KieContainer 直接在内存中加载DRL内容，不写入本地文件 */
    private KieContainer buildKieContainer(List<RuleVersionBaseDto> ruleVersionList) {
        // 生成缓存key
        String cacheKey = generateCacheKey(ruleVersionList);

        // 检查缓存
        if (ruleEngineConfig.getEnableCache() && containerCache.containsKey(cacheKey)) {
            return containerCache.get(cacheKey);
        }

        KieServices kieServices = KieServices.Factory.get();
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem();

        // 直接在内存中添加所有规则内容，使用虚拟路径
        for (int i = 0; i < ruleVersionList.size(); i++) {
            RuleVersionBaseDto ruleVersion = ruleVersionList.get(i);
            if (StringUtils.hasText(ruleVersion.getDrlContent())) {
                // 使用虚拟路径，不会创建实际文件，只在内存中存在
                String virtualPath =
                        String.format(
                                "src/main/resources/rules/rule_%s_%d.drl", ruleVersion.getId(), i);
                kieFileSystem.write(virtualPath, ruleVersion.getDrlContent());

                log.debug("已将规则版本 {} 的DRL内容加载到内存虚拟路径: {}", ruleVersion.getId(), virtualPath);
            }
        }

        // 构建
        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();

        // 检查构建结果
        Results results = kieBuilder.getResults();
        if (results.hasMessages(Message.Level.ERROR)) {
            log.error("规则编译失败，错误信息: {}", results.getMessages());
            throw new IgnoredException(ErrorCode.SYS_ERROR, "规则编译失败: " + results.getMessages());
        }

        // 创建容器
        InternalKieModule kieModule =
                (InternalKieModule) ((KieBuilderImpl) kieBuilder).getKieModule();
        KieContainer kieContainer = kieServices.newKieContainer(kieModule.getReleaseId());

        // 缓存容器
        if (ruleEngineConfig.getEnableCache()) {
            containerCache.put(cacheKey, kieContainer);
            log.debug("规则容器已缓存，缓存key: {}", cacheKey);
        }

        log.info("成功构建规则容器，包含 {} 个规则版本", ruleVersionList.size());
        return kieContainer;
    }

    /** 生成缓存key */
    private String generateCacheKey(List<RuleVersionBaseDto> ruleVersionList) {
        StringBuilder sb = new StringBuilder();
        for (RuleVersionBaseDto ruleVersion : ruleVersionList) {
            sb.append(ruleVersion.getId())
                    .append("_")
                    .append(ruleVersion.getVersionNumber())
                    .append(";");
        }
        return String.valueOf(sb.toString().hashCode());
    }

    /**
     * 从触发的规则中检测冲突
     *
     * @param kieSession KieSession实例
     * @param firedRules 触发的规则数量
     * @param conflictContext 冲突检测上下文
     * @return 是否存在冲突
     */
    @SuppressWarnings("unchecked")
    private boolean detectConflictFromFiredRules(
            KieSession kieSession, int firedRules, Map<String, Object> conflictContext) {
        // 这里可以根据具体的业务逻辑实现更精确的冲突检测
        // 例如：检查是否有相互矛盾的结论、检查规则优先级等

        // 获取触发的规则列表
        List<String> firedRulesList = (List<String>) conflictContext.get("firedRules");

        // 检查是否有多个规则对同一个业务对象产生了不同的结论
        // 这里可以根据具体的HIS业务场景进行定制

        // 简单实现：如果触发了多个规则，认为可能存在冲突
        // 在实际应用中，可以根据规则的结论、优先级、适用范围等进行更精确的冲突判断
        boolean hasConflict = firedRules > 1;

        if (hasConflict) {
            log.warn("检测到潜在规则冲突，触发的规则数量: {}, 触发的规则: {}", firedRules, firedRulesList);
        }

        return hasConflict;
    }

    /**
     * 解析业务数据 将JSON格式的业务数据解析为规则引擎可以处理的对象
     *
     * @param businessData JSON格式的业务数据
     * @return 解析后的业务对象
     */
    private Object parseBusinessData(String businessData) {
        try {
            // 这里可以根据具体的HIS业务数据格式进行解析
            // 例如：患者信息、处方信息、检查结果等

            // 简单实现：将JSON字符串解析为Map
            // 在实际应用中，可以解析为具体的业务对象，如PatientInfo、PrescriptionInfo等
            Map<String, Object> data = new HashMap<>();
            data.put("businessData", businessData);
            data.put("dataType", "json");
            data.put("timestamp", System.currentTimeMillis());

            // 可以在这里添加更多的业务上下文信息
            // 例如：医院ID、科室ID、医生ID等

            log.debug("业务数据解析完成，数据长度: {}", businessData.length());
            return data;

        } catch (Exception e) {
            log.error("业务数据解析失败", e);
            // 返回一个包含原始数据的简单对象
            Map<String, Object> fallbackData = new HashMap<>();
            fallbackData.put("rawData", businessData);
            fallbackData.put("parseError", true);
            return fallbackData;
        }
    }

    /** 清理规则容器缓存 释放内存资源 */
    public void clearCache() {
        int cacheSize = containerCache.size();
        containerCache.clear();
        log.info("已清理规则容器缓存，清理数量: {}", cacheSize);
    }

    /** 获取缓存统计信息 */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("cacheSize", containerCache.size());
        stats.put("cacheEnabled", ruleEngineConfig.getEnableCache());
        stats.put("cacheExpireSeconds", ruleEngineConfig.getCacheExpireSeconds());
        return stats;
    }
}
